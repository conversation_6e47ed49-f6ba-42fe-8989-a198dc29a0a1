/**
 * Copyright 2024 Defense Unicorns
 * SPDX-License-Identifier: AGPL-3.0-or-later OR LicenseRef-Defense-Unicorns-Commercial
 */
import { V1OwnerReference } from "@kubernetes/client-node";
import { K8s, kind } from "pepr";
import {
  Allow,
  IstioAuthorizationPolicy,
  IstioGateway,
  IstioServiceEntry,
  IstioVirtualService,
  IstioWaypoint,
  RemoteProtocol,
  UDSPackage,
} from "../../crd";
import { purgeOrphans } from "../utils";
import { generateAuthorizationPolicy } from "./auth-policy";
import { applySidecarEgressResources, validateEgressGatewayNamespace, purgeSidecarEgressResources } from "./egress-sidecar";
import { istioEgressGatewayNamespace, istioEgressWaypointNamespace, log } from "./istio-resources";
import { IstioState } from "./namespace";
import {
  generateLocalEgressSEName,
  generateLocalEgressServiceEntry,
} from "./service-entry";
import {
  HostPortsProtocol,
  HostResourceMap,
  PackageAction,
  PackageHostMap,
} from "./types";
import { generateWaypoint } from "./waypoint";

// Cache for in-memory sidecar-only shared egress resources from package CRs
export const inMemoryPackageMap: PackageHostMap = {};

// Lock to prevent concurrent updates to the inMemoryPackageMap
let lock = false;
// eslint-disable-next-line prefer-const
let lockQueue: (() => void)[] = [];

// Cache for in-memory ambient egress resources from package CRs
export const inMemoryAmbientPackages: string[] = [];
let ambientLock = false;
// eslint-disable-next-line prefer-const
let ambientLockQueue: (() => void)[] = [];

// Mutexes to prevent concurrent reconciliation operations for each mode
let reconciliationMutex: Promise<void> | null = null;

// Track which packages were included in the last reconciliation
let lastReconciliationPackages: Set<string> = new Set();

// Generation counters for shared egress resources (separate for each mode)
let sidecarGeneration = 0;
let ambientGeneration = 0;

// reconcileSharedEgressResources reconciles the egress resources based on the config
// Handles mode transitions by updating both sidecar and ambient in-memory maps appropriately
export async function reconcileSharedEgressResources(
  hostResourceMap: HostResourceMap | undefined,
  pkgId: string,
  action: PackageAction,
  istioMode: string,
) {
  // Update in-memory maps based on the target mode
  if (istioMode === IstioState.Ambient) {
    // Remove from sidecar map (handles sidecar -> ambient transition)
    await updateInMemoryPackageMap(hostResourceMap, pkgId, PackageAction.Remove);

    // Update ambient package list
    await updateInMemoryAmbientPackages(pkgId, action);
  } else {
    // Update sidecar map
    await updateInMemoryPackageMap(hostResourceMap, pkgId, action);

    // Remove from ambient list (handles ambient -> sidecar transition)
    await updateInMemoryAmbientPackages(pkgId, PackageAction.Remove);
  }

  // Reconcile both modes to ensure proper cleanup and application
  // This handles mode transitions and prevents resource conflicts
  return await performEgressReconciliationWithMutex(pkgId);
}

// Mutex-based reconciliation to prevent overwhelming the operator
export async function performEgressReconciliationWithMutex(pkgId: string): Promise<void> {
  // If there's already a reconciliation in progress, wait for it to complete
  if (reconciliationMutex) {
    try {
      await reconciliationMutex;
      // Check if this package was included in the last reconciliation
      if (lastReconciliationPackages.has(pkgId)) {
        return;
      }
    } catch {
      // If the previous reconciliation failed, we still need to try our own reconciliation
      // Clear the failed mutex so we can start a new one
      reconciliationMutex = null;
    }
  }

  // Start a new reconciliation
  reconciliationMutex = performEgressReconciliation();

  try {
    await reconciliationMutex;
  } catch (e) {
    // Log the error and re-throw to maintain error propagation
    log.error("Egress reconciliation failed", e);
    throw e;
  } finally {
    // Clear the mutex when done
    reconciliationMutex = null;
  }
}

// Perform sidecar egress resources reconciliation
export async function performEgressReconciliation() {
  try {
    // Check if the istioEgressGatewayNamespace exists
    try {
      await validateEgressGatewayNamespace(true);
    } catch (e) {
      throw e;
    }

    sidecarGeneration++;

    // Capture which packages are included in this reconciliation
    lastReconciliationPackages = new Set(Object.keys(inMemoryPackageMap));

    // Apply any sidecar egress resources
    await applySidecarEgressResources(inMemoryPackageMap, sidecarGeneration);

    // Purge any orphaned sidecar shared resources
    await purgeSidecarEgressResources(sidecarGeneration.toString());

  } catch (e) {
    const errText = `Failed to reconcile shared sidecar egress resources`;
    log.error(errText, e);
    throw e;
  }
}

// Perform ambient egress resources reconciliation
export async function performAmbientEgressReconciliation() {
  try {
    // Check if the istioEgressWaypointNamespace exists
    try {
      await K8s(kind.Namespace).Get(istioEgressWaypointNamespace);
    } catch (e) {
      if (e?.status == 404) {
        log.debug(
          `Namespace ${istioEgressWaypointNamespace} not found. Skipping ambient egress resource reconciliation.`,
        );
        return;
      } else {
        throw e;
      }
    }

    ambientGeneration++;

    // Capture which packages are included in this reconciliation
    lastAmbientReconciliationPackages = new Set(inMemoryAmbientPackages);

    // Apply ambient egress resources (waypoint)
    await applyAmbientEgressResources(inMemoryAmbientPackages, ambientGeneration);

    // Purge any orphaned ambient resources (waypoint)
    await purgeOrphans(
      ambientGeneration.toString(),
      istioEgressWaypointNamespace,
      sharedEgressPkgId,
      IstioWaypoint,
      log,
    );
  } catch (e) {
    const errText = `Failed to reconcile shared ambient egress resources`;
    log.error(errText, e);
    throw e;
  }
}

// Update the inMemoryPackageMap with the latest hostResourceMap
export async function updateInMemoryPackageMap(
  hostResourceMap: HostResourceMap | undefined,
  pkgId: string,
  action: PackageAction,
) {
  // Wait for lock to be available using a promise-based queue
  if (lock) {
    await new Promise<void>(resolve => {
      lockQueue.push(resolve);
    });
  }

  try {
    log.debug("Locking egress package map for update");
    lock = true;

    if (action == PackageAction.AddOrUpdate) {
      if (hostResourceMap) {
        // Validate for protocol conflicts before updating
        validateProtocolConflicts(inMemoryPackageMap, hostResourceMap, pkgId);
        // update inMemoryPackageMap
        inMemoryPackageMap[pkgId] = hostResourceMap;
      } else {
        removeEgressResources(pkgId);
      }
    } else if (action == PackageAction.Remove) {
      removeEgressResources(pkgId);
    }
  } catch (e) {
    log.error("Failed to update in memory egress package map for event", action, e);
    throw e;
  } finally {
    // unlock inMemoryPackageMap and notify next waiter
    log.debug("Unlocking egress package map for update");
    lock = false;
    const nextResolve = lockQueue.shift();
    if (nextResolve) {
      nextResolve();
    }
  }
}

// Update the inMemoryAmbientPackages list with the latest package
export async function updateInMemoryAmbientPackages(pkgId: string, action: PackageAction) {
  // Wait for lock to be available using a promise-based queue
  if (ambientLock) {
    await new Promise<void>(resolve => {
      ambientLockQueue.push(resolve);
    });
  }

  try {
    log.debug("Locking ambient package list for update");
    ambientLock = true;

    if (action == PackageAction.AddOrUpdate) {
      inMemoryAmbientPackages.push(pkgId);
    } else if (action == PackageAction.Remove) {
      const index = inMemoryAmbientPackages.indexOf(pkgId);
      if (index > -1) {
        inMemoryAmbientPackages.splice(index, 1);
      }
    }
  } catch (e) {
    log.error("Failed to update in memory ambient package list for event", action, e);
    throw e;
  } finally {
    // unlock inMemoryAmbientPackages and notify next waiter
    log.debug("Unlocking ambient package map for update");
    ambientLock = false;
    const nextResolve = ambientLockQueue.shift();
    if (nextResolve) {
      nextResolve();
    }
  }
}

// Validate that there are no protocol conflicts for the same host/port combination
export function validateProtocolConflicts(
  currentPackageMap: PackageHostMap,
  newHostResourceMap: HostResourceMap,
  newPkgId: string,
): void {
  // Create a map of host:port -> protocol for existing packages (excluding the package being updated)
  const existingHostPortProtocols: Record<string, { protocol: RemoteProtocol; packageId: string }> =
    {};

  for (const [pkgId, hostResourceMap] of Object.entries(currentPackageMap)) {
    // Skip the package being updated since it will be replaced
    if (pkgId === newPkgId) {
      continue;
    }

    for (const [host, hostResource] of Object.entries(hostResourceMap)) {
      for (const portProtocol of hostResource.portProtocol) {
        const key = `${host}:${portProtocol.port}`;
        existingHostPortProtocols[key] = {
          protocol: portProtocol.protocol,
          packageId: pkgId,
        };
      }
    }
  }

  // Check the new host resource map for conflicts
  for (const [host, hostResource] of Object.entries(newHostResourceMap)) {
    for (const portProtocol of hostResource.portProtocol) {
      const key = `${host}:${portProtocol.port}`;
      const existing = existingHostPortProtocols[key];

      if (existing && existing.protocol !== portProtocol.protocol) {
        const errorMsg =
          `Protocol conflict detected for ${host}:${portProtocol.port}. ` +
          `Package "${newPkgId}" wants to use ${portProtocol.protocol} but package "${existing.packageId}" ` +
          `is already using ${existing.protocol} for the same host and port combination.`;
        log.error(errorMsg);
        throw new Error(errorMsg);
      }
    }
  }
}

// Create a host resource map from a UDSPackage
export function createHostResourceMap(pkg: UDSPackage) {
  const hostResourceMap: HostResourceMap = {};

  for (const allow of pkg.spec?.network?.allow ?? []) {
    const hostPortsProtocol = getHostPortsProtocol(allow);

    if (hostPortsProtocol) {
      // Check if the host already exists in the map
      if (!hostResourceMap[hostPortsProtocol.host]) {
        hostResourceMap[hostPortsProtocol.host] = {
          portProtocol: [],
        };
      }

      // Iterate over the ports array to add port/protocol pairs
      for (const port of hostPortsProtocol.ports) {
        // Check if the port/protocol already exists
        const existingPortProtocol = hostResourceMap[hostPortsProtocol.host].portProtocol.find(
          pp => pp.port === port && pp.protocol === hostPortsProtocol.protocol,
        );

        // If it doesn't exist, add it to the list
        if (!existingPortProtocol) {
          hostResourceMap[hostPortsProtocol.host].portProtocol.push({
            port: port,
            protocol: hostPortsProtocol.protocol,
          });
        }
      }
    }
  }

  if (Object.keys(hostResourceMap).length > 0) {
    return hostResourceMap;
  }
  return undefined;
}

// Apply the ambient egress resources
export async function applyAmbientEgressResources(packageList: string[], generation: number) {
  // If no packages using ambient egress, don't create the waypoint
  if (packageList.length === 0) {
    return;
  }

  // Generate the waypoint payload
  const waypoint = generateWaypoint(packageList, generation);

  // Apply waypoint
  log.debug(waypoint, `Applying Waypoint ${waypoint.metadata?.name}`);

  // Apply the Waypoint and force overwrite any existing resource
  await K8s(IstioWaypoint).Apply(waypoint, { force: true });
}

// Validate that the egress waypoint namespace exists
export async function validateEgressWaypoint() {
  // Error if egress waypoint is not enabled in the cluster
  try {
    await K8s(kind.Namespace).Get(istioEgressWaypointNamespace);
  } catch (e) {
    let errText = `Unable to get the egress waypoint namespace ${istioEgressWaypointNamespace}.`;
    if (e.status == 404) {
      errText = `Egress waypoint is not enabled in the cluster. Please enable the egress waypoint and retry.`;
    }
    log.error(errText);
    throw new Error(errText);
  }
}

// Create package owned ambient egress resources
export async function createAmbientWorkloadEgressResources(
  hostResourceMap: HostResourceMap,
  egressRequested: Allow[],
  pkgName: string,
  namespace: string,
  generation: string,
  ownerRefs: V1OwnerReference[],
) {
  // Add service entry for each defined host
  for (const host of Object.keys(hostResourceMap)) {
    // Create Service Entry
    const serviceEntry = generateLocalEgressServiceEntry(
      host,
      hostResourceMap[host],
      pkgName,
      namespace,
      generation,
      ownerRefs,
      true,
    );

    log.debug(serviceEntry, `Applying Service Entry ${serviceEntry.metadata?.name}`);

    // Apply the ServiceEntry and force overwrite any existing resource
    await K8s(IstioServiceEntry).Apply(serviceEntry, { force: true });
  }

  // Create Authorization Policy for service entry, if serviceAccount is specified
  for (const allow of egressRequested) {
    if (allow.serviceAccount) {
      const serviceAccount = allow.serviceAccount;
      const hostPortsProtocol = getHostPortsProtocol(allow);
      if (!hostPortsProtocol) {
        continue;
      }
      const { host, ports, protocol } = hostPortsProtocol;
      const portsProtocol = ports.map(port => ({ port, protocol }));

      // Validate serviceAccount exists - else all egress traffic will fail
      try {
        await K8s(kind.ServiceAccount).InNamespace(namespace).Get(serviceAccount);
      } catch {
        const errText = `ServiceAccount ${serviceAccount} does not exist in namespace ${namespace}. Please create the ServiceAccount and retry.`;
        log.error(errText);
        throw new Error(errText);
      }

      // Create Authorization Policy
      const authPolicy = generateAuthorizationPolicy(
        host,
        pkgName,
        namespace,
        generation,
        ownerRefs,
        generateLocalEgressSEName(pkgName, portsProtocol, host),
        serviceAccount,
      );

      log.debug(authPolicy, `Applying Authorization Policy ${authPolicy.metadata?.name}`);

      // Apply the AuthorizationPolicy and force overwrite any existing resource
      await K8s(IstioAuthorizationPolicy).Apply(authPolicy, { force: true });
    }
  }
}

// Get the host, ports, and protocol from an Allow
export function getHostPortsProtocol(allow: Allow) {
  let hostPortsProtocol: HostPortsProtocol | undefined = undefined;

  const host = allow.remoteHost;
  const protocol = allow.remoteProtocol ?? RemoteProtocol.TLS;

  // reconcile ports
  let ports = [];
  if (allow.ports) {
    ports = allow.ports;
  } else if (allow.port) {
    ports = [allow.port];
  } else {
    ports = [443];
  }

  if (host) {
    hostPortsProtocol = {
      host,
      ports,
      protocol,
    };
  }
  return hostPortsProtocol;
}

// Remove egress resources for a package
export function removeEgressResources(pkgId: string) {
  if (inMemoryPackageMap[pkgId]) {
    delete inMemoryPackageMap[pkgId];
  } else {
    log.debug("No egress resources found for package", pkgId);
  }
}

// Check if egress is requested from the network from the Allow list
export function egressRequestedFromNetwork(allowList: Allow[]) {
  return allowList.filter(allow => {
    return allow.remoteHost;
  });
}
